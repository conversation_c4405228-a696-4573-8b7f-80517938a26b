import cors from 'cors';
import express from 'express';
import { AccessToken, RoomServiceClient } from 'livekit-server-sdk';

// Initialize LiveKit Room Service Client
const roomService = new RoomServiceClient(
  process.env.LIVEKIT_URL!,
  process.env.LIVEKIT_API_KEY!,
  process.env.LIVEKIT_API_SECRET!
);

// Create token for regular participants
const createToken = async (roomName?: string, participantName?: string) => {
  const room = roomName || 'quickstart-room';
  const participant = participantName || 'quickstart-username';

  const at = new AccessToken(process.env.LIVEKIT_API_KEY, process.env.LIVEKIT_API_SECRET, {
    identity: participant,
    // Token to expire after 10 minutes
    ttl: '10m',
  });
  at.addGrant({ roomJoin: true, room });
  return await at.toJwt();
};

// Create token for AI agent
const createAgentToken = async (roomName: string) => {
  const agentIdentity = `ai-agent-${Date.now()}`;

  const at = new AccessToken(process.env.LIVEKIT_API_KEY, process.env.LIVEKIT_API_SECRET, {
    identity: agentIdentity,
    // Longer TTL for agents
    ttl: '1h',
  });

  // Grant permissions for agent
  at.addGrant({
    roomJoin: true,
    room: roomName,
    canPublish: true,
    canSubscribe: true,
    canPublishData: true,
  });

  return await at.toJwt();
};

const app = express();
const port = 3000;

app.use(cors());

app.get('/getToken', async (req, res) => {
  res.send(await createToken());
});

app.listen(port, () => {
  console.log(`Server listening on port ${port}`);
});
