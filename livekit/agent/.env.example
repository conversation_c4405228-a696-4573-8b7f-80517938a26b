# LiveKit Configuration (Required)
LIVEKIT_URL=wss://your-livekit-server.com
LIVEKIT_API_KEY=your-livekit-api-key
LIVEKIT_API_SECRET=your-livekit-api-secret

# OpenAI Configuration (Required if not using Azure)
OPENAI_API_KEY=your-openai-api-key
OPENAI_MODEL=gpt-4o-realtime-preview-2024-10-01

# Azure OpenAI Configuration (Optional - alternative to OpenAI)
# AZURE_OPENAI_ENDPOINT=https://your-resource.openai.azure.com
# AZURE_OPENAI_DEPLOYMENT=your-deployment-name
# AZURE_OPENAI_API_KEY=your-azure-api-key
# AZURE_OPENAI_ENTRA_TOKEN=your-entra-token

# Agent Behavior Configuration (Optional)
AGENT_SYSTEM_PROMPT="You are a helpful AI assistant for patient conversations."
AGENT_INITIAL_GREETING="Hello! I'm your AI assistant. How can I help you today?"
AGENT_VOICE=alloy
AGENT_TEMPERATURE=0.7
AGENT_LOG_LEVEL=info
AGENT_MAX_DURATION=30

# Audio Configuration (Optional)
AUDIO_SAMPLE_RATE=24000
AUDIO_CHANNELS=1

# Development Configuration
NODE_ENV=development
