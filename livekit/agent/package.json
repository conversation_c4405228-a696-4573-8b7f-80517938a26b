{"name": "livekit-ai-agent", "version": "1.0.0", "type": "module", "private": true, "description": "LiveKit AI Agent for real-time conversations", "main": "dist/agent.js", "scripts": {"build": "tsc", "clean": "rm -rf dist", "dev": "tsx src/agent.ts dev", "start": "tsx src/agent.ts start", "connect": "tsx src/agent.ts connect", "lint": "eslint src/**/*.ts", "type-check": "tsc --noEmit"}, "dependencies": {"@livekit/agents": "^0.7.7", "@livekit/agents-plugin-openai": "^0.7.7", "@livekit/agents-plugin-deepgram": "^0.7.7", "@livekit/agents-plugin-silero": "^0.7.7", "@livekit/agents-plugin-livekit": "^0.7.7", "livekit-server-sdk": "^2.13.1", "zod": "^3.23.8", "dotenv": "^16.4.5"}, "devDependencies": {"@types/node": "^22.5.5", "@typescript-eslint/eslint-plugin": "^8.0.0", "@typescript-eslint/parser": "^8.0.0", "eslint": "^9.0.0", "tsx": "^4.19.2", "typescript": "^5.0.0"}, "engines": {"node": ">=18.0.0"}}