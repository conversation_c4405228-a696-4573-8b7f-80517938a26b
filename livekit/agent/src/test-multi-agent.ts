// Test script for Multi-Agent System

import { multiAgentConfig, validateMultiAgentConfig, getAgentConfig } from './multi-agent-config.js';
import { logger } from './logger.js';

// Test scenarios for different intents
const testScenarios = [
  {
    name: 'Booking Appointment',
    inputs: [
      'Bonjour, je voudrais prendre rendez-vous',
      'De<PERSON>in matin si possible',
      'Oui, 10h30 me convient parfaitement',
    ],
    expectedFlow: ['router', 'booking', 'booking'],
    expectedIntents: ['GREETING', 'BOOK', 'BOOK'],
  },
  {
    name: 'Canceling Appointment',
    inputs: [
      'Je dois annuler mon rendez-vous',
      'O<PERSON>, je confirme l\'annulation',
    ],
    expectedFlow: ['router', 'cancel'],
    expectedIntents: ['CANCEL', 'CANCEL'],
  },
  {
    name: 'General Questions',
    inputs: [
      'Quels sont vos horaires d\'ouverture ?',
      '<PERSON><PERSON>ien coûte une consultation ?',
      'Où êtes-vous situés ?',
    ],
    expectedFlow: ['router', 'faq', 'faq'],
    expectedIntents: ['QUESTION', 'QUESTION', 'QUESTION'],
  },
  {
    name: 'Greeting and Mixed Conversation',
    inputs: [
      'Bonjour',
      'Je voudrais des informations sur vos tarifs',
      'Et aussi prendre rendez-vous',
    ],
    expectedFlow: ['router', 'router', 'router'],
    expectedIntents: ['GREETING', 'QUESTION', 'BOOK'],
  },
];

// Mock agent classes for testing
class MockRouterAgent {
  detectIntent(input: string): string {
    const lowerInput = input.toLowerCase();
    
    if (lowerInput.includes('rendez-vous') || lowerInput.includes('rdv') || lowerInput.includes('réserver')) {
      return 'BOOK';
    }
    if (lowerInput.includes('annuler') || lowerInput.includes('supprimer')) {
      return 'CANCEL';
    }
    if (lowerInput.includes('?') || lowerInput.includes('horaire') || lowerInput.includes('prix') || lowerInput.includes('tarif')) {
      return 'QUESTION';
    }
    if (lowerInput.includes('bonjour') || lowerInput.includes('salut')) {
      return 'GREETING';
    }
    
    return 'UNKNOWN';
  }
}

async function testConfiguration(): Promise<void> {
  logger.info('🧪 Testing Multi-Agent Configuration');
  
  try {
    // Test configuration validation
    const errors = validateMultiAgentConfig(multiAgentConfig);
    
    if (errors.length > 0) {
      logger.error('❌ Configuration validation failed:', { errors });
      return;
    }
    
    logger.info('✅ Configuration validation passed');
    
    // Test individual agent configurations
    const agentNames = ['router', 'booking', 'cancel', 'faq'];
    
    for (const agentName of agentNames) {
      const agentConfig = getAgentConfig(agentName, multiAgentConfig);
      if (agentConfig) {
        logger.info(`✅ ${agentName} agent configuration loaded`, {
          enabled: agentConfig.enabled,
          config: Object.keys(agentConfig),
        });
      } else {
        logger.warn(`⚠️  ${agentName} agent configuration not found`);
      }
    }
    
    // Display current configuration
    logger.info('📋 Current Multi-Agent Configuration:', {
      enabledAgents: agentNames.filter(name => {
        const config = getAgentConfig(name, multiAgentConfig);
        return config?.enabled;
      }),
      sessionConfig: multiAgentConfig.session,
      voiceConfig: multiAgentConfig.voice,
    });
    
  } catch (error) {
    logger.error('❌ Configuration test failed:', { error });
  }
}

async function testIntentDetection(): Promise<void> {
  logger.info('🎯 Testing Intent Detection');
  
  const router = new MockRouterAgent();
  const testInputs = [
    { input: 'Bonjour', expected: 'GREETING' },
    { input: 'Je voudrais prendre rendez-vous', expected: 'BOOK' },
    { input: 'Je dois annuler mon rdv', expected: 'CANCEL' },
    { input: 'Quels sont vos horaires ?', expected: 'QUESTION' },
    { input: 'Combien ça coûte ?', expected: 'QUESTION' },
    { input: 'Quelque chose de bizarre', expected: 'UNKNOWN' },
  ];
  
  let passed = 0;
  let failed = 0;
  
  for (const test of testInputs) {
    const detected = router.detectIntent(test.input);
    const success = detected === test.expected;
    
    if (success) {
      passed++;
      logger.info(`✅ Intent detection: "${test.input}" -> ${detected}`);
    } else {
      failed++;
      logger.error(`❌ Intent detection failed: "${test.input}" -> ${detected} (expected: ${test.expected})`);
    }
  }
  
  logger.info(`🎯 Intent Detection Results: ${passed} passed, ${failed} failed`);
}

async function testConversationFlows(): Promise<void> {
  logger.info('💬 Testing Conversation Flows');
  
  const router = new MockRouterAgent();
  
  for (const scenario of testScenarios) {
    logger.info(`\n📝 Testing scenario: ${scenario.name}`);
    
    for (let i = 0; i < scenario.inputs.length; i++) {
      const input = scenario.inputs[i];
      const expectedIntent = scenario.expectedIntents[i];
      
      const detectedIntent = router.detectIntent(input);
      const success = detectedIntent === expectedIntent;
      
      if (success) {
        logger.info(`  ✅ Step ${i + 1}: "${input}" -> ${detectedIntent}`);
      } else {
        logger.error(`  ❌ Step ${i + 1}: "${input}" -> ${detectedIntent} (expected: ${expectedIntent})`);
      }
    }
  }
}

async function testKnowledgeBase(): Promise<void> {
  logger.info('📚 Testing Knowledge Base');
  
  const kb = multiAgentConfig.faq.knowledgeBase;
  
  const requiredFields = ['hours', 'address', 'phone', 'email', 'pricing', 'services'];
  let allFieldsPresent = true;
  
  for (const field of requiredFields) {
    if (kb[field as keyof typeof kb]) {
      logger.info(`✅ Knowledge base field '${field}' is configured`);
    } else {
      logger.error(`❌ Knowledge base field '${field}' is missing`);
      allFieldsPresent = false;
    }
  }
  
  if (allFieldsPresent) {
    logger.info('✅ All knowledge base fields are configured');
  } else {
    logger.warn('⚠️  Some knowledge base fields are missing');
  }
  
  // Test pricing structure
  const pricingEntries = Object.entries(kb.pricing);
  logger.info(`💰 Pricing configured for ${pricingEntries.length} services:`, {
    services: pricingEntries.map(([service, price]) => `${service}: ${price}`),
  });
  
  // Test services list
  logger.info(`🏥 Available services (${kb.services.length}):`, {
    services: kb.services,
  });
}

async function testAgentCapabilities(): Promise<void> {
  logger.info('🤖 Testing Agent Capabilities');
  
  const capabilities = {
    router: ['intent_detection', 'conversation_routing'],
    booking: ['appointment_creation', 'date_validation', 'time_slot_management'],
    cancel: ['appointment_cancellation', 'rescheduling'],
    faq: ['information_retrieval', 'knowledge_base_query'],
  };
  
  for (const [agentName, agentCapabilities] of Object.entries(capabilities)) {
    const config = getAgentConfig(agentName, multiAgentConfig);
    
    if (config?.enabled) {
      logger.info(`✅ ${agentName} agent capabilities:`, {
        capabilities: agentCapabilities,
        enabled: true,
      });
    } else {
      logger.warn(`⚠️  ${agentName} agent is disabled`, {
        capabilities: agentCapabilities,
        enabled: false,
      });
    }
  }
}

async function runMultiAgentTests(): Promise<void> {
  console.log('🚀 Multi-Agent System Test Suite\n');
  
  try {
    await testConfiguration();
    console.log('');
    
    await testIntentDetection();
    console.log('');
    
    await testConversationFlows();
    console.log('');
    
    await testKnowledgeBase();
    console.log('');
    
    await testAgentCapabilities();
    console.log('');
    
    logger.info('🎉 All multi-agent tests completed!');
    
    console.log('\n📋 Next Steps:');
    console.log('1. Start your LiveKit server');
    console.log('2. Start the enhanced server: cd ../server && bun run server.ts');
    console.log('3. Start the multi-agent system: bun run dev:multi');
    console.log('4. Create a patient room via POST /createPatientRoom');
    console.log('5. Connect your client and test the conversation flows');
    console.log('\n💡 Available conversation patterns:');
    console.log('   - "Bonjour" (greeting)');
    console.log('   - "Je voudrais prendre rendez-vous" (booking)');
    console.log('   - "Je dois annuler mon rendez-vous" (cancellation)');
    console.log('   - "Quels sont vos horaires ?" (FAQ)');
    
  } catch (error) {
    logger.error('❌ Multi-agent test suite failed:', { error });
    process.exit(1);
  }
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
  runMultiAgentTests().catch((error) => {
    logger.error('Test suite failed:', error);
    process.exit(1);
  });
}
