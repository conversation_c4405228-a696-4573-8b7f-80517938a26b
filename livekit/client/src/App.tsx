import {
  ControlBar,
  GridLayout,
  ParticipantTile,
  RoomAudioRenderer,
  useTracks,
  RoomContext,
} from '@livekit/components-react';
import { Room, Track } from 'livekit-client';
import '@livekit/components-styles';
import { useEffect, useState } from 'react';
import PatientApp from './PatientApp';

const serverUrl = 'http://localhost:7880';

export default function App() {
  const [appMode, setAppMode] = useState<'select' | 'original' | 'patient'>('select');

  // App mode selection
  if (appMode === 'select') {
    return (
      <div style={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        height: '100vh',
        fontFamily: 'Arial, sans-serif',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
      }}>
        <div style={{
          background: 'white',
          padding: '40px',
          borderRadius: '15px',
          boxShadow: '0 10px 25px rgba(0, 0, 0, 0.2)',
          textAlign: 'center',
          maxWidth: '500px'
        }}>
          <h1 style={{ marginBottom: '20px', color: '#333' }}>
            LiveKit Demo
          </h1>
          <p style={{ marginBottom: '30px', color: '#666', lineHeight: '1.6' }}>
            Choose how you want to experience the LiveKit integration:
          </p>

          <div style={{ display: 'flex', gap: '20px', flexDirection: 'column' }}>
            <button
              onClick={() => setAppMode('patient')}
              style={{
                padding: '15px 25px',
                background: '#28a745',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                fontSize: '16px',
                cursor: 'pointer',
                transition: 'background 0.3s'
              }}
              onMouseOver={(e) => e.currentTarget.style.background = '#218838'}
              onMouseOut={(e) => e.currentTarget.style.background = '#28a745'}
            >
              🤖 Patient Consultation with AI Agent
            </button>

            <button
              onClick={() => setAppMode('original')}
              style={{
                padding: '15px 25px',
                background: '#007bff',
                color: 'white',
                border: 'none',
                borderRadius: '8px',
                fontSize: '16px',
                cursor: 'pointer',
                transition: 'background 0.3s'
              }}
              onMouseOver={(e) => e.currentTarget.style.background = '#0056b3'}
              onMouseOut={(e) => e.currentTarget.style.background = '#007bff'}
            >
              📹 Original Video Conference
            </button>
          </div>

          <div style={{ marginTop: '30px', fontSize: '14px', color: '#666' }}>
            <p><strong>Patient Consultation:</strong> 1:1 conversation with AI agent</p>
            <p><strong>Video Conference:</strong> Traditional multi-participant room</p>
          </div>
        </div>
      </div>
    );
  }

  if (appMode === 'patient') {
    return <PatientApp />;
  }

  // Original app implementation
  return <OriginalApp />;
}

function OriginalApp() {
  const [room] = useState(() => new Room({
    // Optimize video quality for each participant's screen
    adaptiveStream: true,
    // Enable automatic audio/video quality optimization
    dynacast: true,
  }));

  const [token, setToken] = useState<string | null>(null);
  useEffect(() => {
    fetch(`${serverUrl}/getToken`)
      .then((res) => res.text())
      .then(setToken);
  }, []);

  // Connect to room
  useEffect(() => {
    if (!token) return;
    let mounted = true;

    const connect = async () => {
      if (mounted) {
        await room.connect('ws://localhost:7880', token);
      }
    };
    connect();

    return () => {
      mounted = false;
      room.disconnect();
    };
  }, [room, token]);

  if (!token) return <div>Loading...</div>;

  return (
    <RoomContext.Provider value={room}>
      <div data-lk-theme="default" style={{ width: "100vw", height: '100vh' }}>
        {/* Your custom component with basic video conferencing functionality. */}
        <MyVideoConference />
        {/* The RoomAudioRenderer takes care of room-wide audio for you. */}
        <RoomAudioRenderer />
        {/* Controls for the user to start/stop audio, video, and screen share tracks */}
        <ControlBar />
      </div>
    </RoomContext.Provider>
  );
}

function MyVideoConference() {
  // `useTracks` returns all camera and screen share tracks. If a user
  // joins without a published camera track, a placeholder track is returned.
  const tracks = useTracks(
    [
      { source: Track.Source.Camera, withPlaceholder: true },
      { source: Track.Source.ScreenShare, withPlaceholder: false },
    ],
    { onlySubscribed: false },
  );
  return (
    <GridLayout tracks={tracks} style={{ height: 'calc(100vh - var(--lk-control-bar-height))' }}>
      {/* The GridLayout accepts zero or one child. The child is used
      as a template to render all passed in tracks. */}
      <ParticipantTile />
    </GridLayout>
  );
}
